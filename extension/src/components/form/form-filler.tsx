import React, { useState, useEffect } from 'react'
import { useAppStore } from '~/store'
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'



interface FormFillerProps {
  selectedForm: any | null
}

export function FormFiller({ selectedForm }: FormFillerProps) {
  const { 
    projects, 
    selectedProject, 
    selectProject, 
    fillForm,
    optimizeWithSeparatedContent,
    aiOptimizing,
    error 
  } = useAppStore()

  const [isFillingForm, setIsFillingForm] = useState(false)
  const [fillResult, setFillResult] = useState<any>(null)
  const [customMapping, setCustomMapping] = useState<any>({})

  // 清空表单字段内容
  const clearFormFields = async () => {
    if (!selectedForm) return

    try {
      console.log('[FormFiller] 开始清空表单字段...')
      
      // 向content script发送清空表单的消息
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      if (!tab.id) return

      await chrome.tabs.sendMessage(tab.id, {
        type: 'CLEAR_FORM_FIELDS',
        formIndex: selectedForm.index
      })
      
      console.log('[FormFiller] 表单字段清空完成')
    } catch (error) {
      console.error('[FormFiller] 清空表单字段失败:', error)
      // 清空失败不阻止后续操作，只记录错误
    }
  }

  // 一键自动填充
  const handleAutoFill = async () => {
    if (!selectedForm || !selectedProject) return

    setIsFillingForm(true)
    setFillResult(null)

    try {
      // 先清空表单已填充的内容
      await clearFormFields()
      
      const result = await fillForm(selectedForm.index, selectedProject)
      setFillResult(result)
    } catch (error) {
      // 错误已在store中处理
    } finally {
      setIsFillingForm(false)
    }
  }

  // 解析AI返回的JSON内容，处理可能的markdown代码块格式
  const parseAIResponse = (content: string): any => {
    // 移除可能的markdown代码块标记
    let cleanedContent = content.trim()
    
    // 移除 ```json 开头和 ``` 结尾
    if (cleanedContent.startsWith('```json')) {
      cleanedContent = cleanedContent.replace(/^```json\s*/, '')
    }
    if (cleanedContent.startsWith('```')) {
      cleanedContent = cleanedContent.replace(/^```\s*/, '')
    }
    if (cleanedContent.endsWith('```')) {
      cleanedContent = cleanedContent.replace(/\s*```$/, '')
    }
    
    // 尝试解析JSON
    try {
      return JSON.parse(cleanedContent)
    } catch (error) {
      // 如果直接解析失败，尝试提取可能的JSON部分
      const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0])
        } catch (innerError) {
          throw new Error(`无法解析AI返回的JSON内容: ${innerError.message}`)
        }
      }
      throw new Error(`AI返回的内容不是有效的JSON格式: ${error.message}`)
    }
  }

  // AI填充处理 - 直接使用AI返回的JSON填充
  const handleAIFill = async () => {
    if (!selectedForm || !selectedProject) return

    setIsFillingForm(true)
    setFillResult(null)

    try {
      console.log('[FormFiller] 开始AI整体优化...')
      
      // 先清空表单已填充的内容
      await clearFormFields()
      
      // 构建项目和表单信息（作为内容）
      const projectContent = `
项目信息：
- 名称：${selectedProject.name}
- 域名：${selectedProject.domain}
- 描述：${selectedProject.info?.introduction || ''}
- 分类：${selectedProject.category || ''}

表单字段：
${selectedForm.fields.map((field: any) => 
  `- ${field.name}: ${field.label || field.placeholder || '未知字段'} (${field.type}${field.required ? ', 必填' : ''})`
).join('\n')}`

      // 构建生成指令（作为prompt）
      const instruction = `请为上述项目和表单字段生成优化的填充内容。

要求：
1. 符合专业正式的风格
2. 准确反映项目特点
3. 适合字段类型和长度限制

请以JSON格式返回，使用字段的name作为key，优化后的内容作为value：
{
  "fieldName1": "优化后的内容1",
  "fieldName2": "优化后的内容2"
}`

      console.log('[FormFiller] 项目内容:', projectContent)
      console.log('[FormFiller] 生成指令:', instruction)

      // 使用分离的内容和指令调用AI
      const optimizedContent = await optimizeWithSeparatedContent(projectContent, instruction)

      console.log('[FormFiller] AI优化原始结果:', optimizedContent)

      // 解析AI返回的JSON内容
      const optimizedFields = parseAIResponse(optimizedContent)
      console.log('[FormFiller] AI优化解析结果:', optimizedFields)

      // 直接构建映射，为AI返回的每个字段创建自定义转换
      const fieldMapping: any = {}
      
      for (const [fieldName, fieldValue] of Object.entries(optimizedFields)) {
        if (typeof fieldValue === 'string' && fieldValue.trim()) {
          fieldMapping[fieldName] = {
            projectField: 'custom',
            transform: () => fieldValue
          }
          console.log(`[FormFiller] 字段 ${fieldName} 使用AI内容:`, fieldValue)
        }
      }

      console.log('[FormFiller] AI字段映射:', fieldMapping)
      console.log('[FormFiller] 映射字段数量:', Object.keys(fieldMapping).length, '/', selectedForm.fields.length)

      // 直接使用AI优化的数据进行填充
      const result = await fillForm(selectedForm.index, selectedProject, fieldMapping)
      setFillResult(result)
      
      console.log('[FormFiller] 表单填充完成:', result)
      
      // 如果填充失败，提供更详细的错误信息
      if (!result.success) {
        console.error('[FormFiller] 填充失败详情:', {
          errors: result.errors,
          warnings: result.warnings,
          filledFields: result.filledFields,
          totalFields: selectedForm.fields.length
        })
      }
    } catch (error) {
      console.error('[FormFiller] AI填充失败:', error)
      // AI失败时不填充，设置错误状态
      setFillResult({
        success: false,
        filledFields: [],
        errors: [`AI优化失败: ${error instanceof Error ? error.message : '未知错误'}`],
        warnings: []
      })
    } finally {
      setIsFillingForm(false)
    }
  }



  if (!selectedForm) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <div className="text-muted-foreground mb-4">
            <svg className="w-12 h-12 mx-auto text-muted-foreground/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <p className="text-muted-foreground">请先选择要填充的表单</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">表单填充</h2>
      </div>

      {error && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
          {error}
        </div>
      )}

      {fillResult && (
        <Card className={fillResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          <CardContent className="py-4">
            <div className="flex items-center gap-2 mb-2">
              {fillResult.success ? (
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <span className={`font-medium ${fillResult.success ? 'text-green-800' : 'text-red-800'}`}>
                {fillResult.success ? '填充成功' : '填充失败'}
              </span>
            </div>

            {fillResult.filledFields.length > 0 && (
              <p className="text-sm text-green-700 mb-2">
                成功填充了 {fillResult.filledFields.length} 个字段: {fillResult.filledFields.join(', ')}
              </p>
            )}

            {fillResult.errors.length > 0 && (
              <div className="text-sm text-red-700">
                <p className="font-medium mb-1">错误信息:</p>
                <ul className="list-disc list-inside space-y-1">
                  {fillResult.errors.map((error: string, index: number) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {fillResult.warnings.length > 0 && (
              <div className="text-sm text-yellow-700 mt-2">
                <p className="font-medium mb-1">警告信息:</p>
                <ul className="list-disc list-inside space-y-1">
                  {fillResult.warnings.map((warning: string, index: number) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 选择项目 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">选择项目</CardTitle>
          <CardDescription>选择要用于填充表单的项目</CardDescription>
        </CardHeader>
        <CardContent>
          {projects.length === 0 ? (
            <p className="text-muted-foreground">没有可用的项目，请先添加项目</p>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                {projects.slice(0, 4).map((project) => (
                  <div
                    key={project.id}
                    className={`p-2 rounded border cursor-pointer transition-colors ${
                      selectedProject?.id === project.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:bg-accent'
                    }`}
                    onClick={() => selectProject(project)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm truncate">{project.name}</h4>
                          {selectedProject?.id === project.id && (
                            <svg className="w-4 h-4 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground truncate">{project.domain}</p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {projects.length > 4 && (
                  <div className="p-2 text-center border border-dashed rounded">
                    <p className="text-xs text-muted-foreground">
                      还有 {projects.length - 4} 个项目...
                    </p>
                  </div>
                )}
              </div>

              {/* 项目详细信息 */}
              {selectedProject && (
                <div className="mt-4 p-3 bg-muted/50 rounded-md">
                  <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    项目信息
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">项目名称:</span>
                      <span className="font-medium">{selectedProject.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">域名:</span>
                      <span className="text-right break-all max-w-[60%]">{selectedProject.domain}</span>
                    </div>
                    {selectedProject.category && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">分类:</span>
                        <span>{selectedProject.category}</span>
                      </div>
                    )}
                    {selectedProject.info?.introduction && (
                      <div>
                        <span className="text-muted-foreground">描述:</span>
                        <p className="mt-1 text-xs leading-relaxed text-muted-foreground">
                          {selectedProject.info.introduction.length > 100 
                            ? `${selectedProject.info.introduction.substring(0, 100)}...` 
                            : selectedProject.info.introduction}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>



      {/* 操作按钮 */}
      <div className="flex gap-3">
        <Button
          onClick={handleAutoFill}
          disabled={!selectedProject || isFillingForm}
          variant="outline"
          className="flex-1"
        >
          {isFillingForm ? (
            <>
              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              填充中...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              一键自动填充
            </>
          )}
        </Button>
        
        <Button
          onClick={handleAIFill}
          disabled={!selectedProject || isFillingForm}
          className="flex-1"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          AI 智能填充
        </Button>
      </div>


    </div>
  )
}