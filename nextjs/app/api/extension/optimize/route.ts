import { NextRequest, NextResponse } from "next/server";
import { getUserByApi<PERSON>ey } from "@/models/user";
import { z } from "zod";

const optimizeContentSchema = z.object({
  text: z.string().min(1, "Text is required"),
  prompt: z.string().min(1, "Prompt is required"),
  context: z.string().optional(),
  max_length: z.number().optional(),
  tone: z.enum(["professional", "casual", "marketing", "technical"]).optional()
});

// 导出一个异步函数，用于处理POST请求
export async function POST(request: NextRequest) {
  let body: any;

  try {
    // 获取请求头中的authorization字段
    const authHeader = request.headers.get("authorization");
    // 获取请求的body
    body = await request.json();

    // Validate request body first - don't log the entire body for security
    console.log("Validating request body for content optimization");
    const validation = optimizeContentSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Validation error",
          errors: validation.error.errors
        },
        { status: 400 }
      );
    }
    
    const { text, prompt, context, max_length, tone } = validation.data;
    
    // Determine AI credentials to use
    let aiApiKey: string | undefined;
    let aiModelName: string;
    let aiApiBaseUrl: string;
    let userTier = "free";
    
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const apiKey = authHeader.replace("Bearer ", "").trim();

      if (apiKey) {
        try {
          // Try to validate API key and get user
          const user = await getUserByApiKey(apiKey);

          if (user && user.uuid) {
            // Authenticated user - use premium AI credentials
            aiApiKey = process.env.AI_API_KEY;
            aiModelName = process.env.AI_MODEL_NAME || "gpt-3.5-turbo";
            aiApiBaseUrl = process.env.AI_API_BASE_URL || "https://api.openai.com/v1";
            userTier = "premium";
          }
        } catch (authError) {
          console.error("API key validation error:", authError);
          // Continue with free tier on auth error
        }
      }
    }
    
    // If no authenticated user, use free AI credentials
    if (userTier === "free") {
      aiApiKey = process.env.FREE_AI_API_KEY;
      aiModelName = process.env.FREE_AI_MODEL_NAME || "gpt-3.5-turbo";
      aiApiBaseUrl = process.env.FREE_AI_API_BASE_URL || "https://api.openai.com/v1";
    }
    
    if (!aiApiKey) {
      console.error(`${userTier.toUpperCase()}_AI_API_KEY not configured`);
      return NextResponse.json(
        {
          success: false,
          message: "AI service is not available. Please try again later.",
          error: "AI_API_KEY_NOT_CONFIGURED"
        },
        { status: 503 }
      );
    }

    // Validate AI API configuration
    if (!aiModelName || !aiApiBaseUrl) {
      console.error(`AI API configuration incomplete for ${userTier} tier`);
      return NextResponse.json(
        {
          success: false,
          message: "AI service configuration is incomplete. Please try again later.",
          error: "AI_CONFIG_INCOMPLETE"
        },
        { status: 503 }
      );
    }
    
    // Construct optimization prompt
    let systemPrompt = `You are a professional content optimizer. ${prompt}`;
    
    if (tone) {
      systemPrompt += ` Use a ${tone} tone.`;
    }
    
    if (max_length) {
      systemPrompt += ` Keep the content under ${max_length} characters.`;
    }
    
    if (context) {
      systemPrompt += ` Additional context: ${context}`;
    }
    
    // Apply tier-specific limitations for free users
    let maxTokens = max_length ? Math.min(max_length * 2, 1000) : 1000;
    if (userTier === "free") {
      maxTokens = Math.min(maxTokens, 500); // Limit free users to 500 tokens
    }
    
    // Call AI API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const aiResponse = await fetch(`${aiApiBaseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${aiApiKey}`,
        },
        body: JSON.stringify({
          model: aiModelName,
          messages: [
            {
              role: "system",
              content: systemPrompt
            },
            {
              role: "user",
              content: `Please optimize this text: "${text}"`
            }
          ],
          max_tokens: maxTokens,
          temperature: userTier === "free" ? 0.5 : 0.7 // Lower creativity for free tier
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!aiResponse.ok) {
        console.error("AI API error:", aiResponse.status, aiResponse.statusText);
        return NextResponse.json(
          {
            success: false,
            message: "AI service returned an error. Please try again later.",
            error: "AI_API_ERROR",
            details: `${aiResponse.status}: ${aiResponse.statusText}`
          },
          { status: 502 }
        );
      }
    
    const aiResult = await aiResponse.json();
    console.log("AI API response:", aiResult);
    
    if (aiResult.choices && aiResult.choices[0]?.message?.content) {
      const optimizedText = aiResult.choices[0].message.content.trim();
      console.l
      
      // Analyze improvements
      const improvements = analyzeImprovements(text, optimizedText, prompt);
      
      return NextResponse.json({
        success: true,
        optimized_text: optimizedText,
        original_length: text.length,
        optimized_length: optimizedText.length,
        improvements,
        confidence: calculateConfidence(text, optimizedText, improvements),
        source: "ai",
        tier: userTier
      });
    } else {
      console.error("AI API returned invalid response format");
      return NextResponse.json(
        {
          success: false,
          message: "AI service returned an invalid response. Please try again later.",
          error: "AI_INVALID_RESPONSE"
        },
        { status: 502 }
      );
    }
    } catch (aiError) {
      clearTimeout(timeoutId);
      console.error("AI API request failed:", aiError);

      return NextResponse.json(
        {
          success: false,
          message: "AI service is currently unavailable. Please try again later.",
          error: "AI_REQUEST_FAILED"
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error("Extension optimize content error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error. Please try again later.",
        error: "INTERNAL_ERROR"
      },
      { status: 500 }
    );
  }
}



function analyzeImprovements(original: string, optimized: string, prompt: string): string[] {
  const improvements = [];
  
  // Length comparison
  if (optimized.length !== original.length) {
    if (optimized.length < original.length) {
      improvements.push("Reduced text length for clarity");
    } else {
      improvements.push("Expanded content for better detail");
    }
  }
  
  // Grammar and style improvements (basic heuristics)
  const originalSentences = original.split(/[.!?]+/).length;
  const optimizedSentences = optimized.split(/[.!?]+/).length;
  
  if (optimizedSentences !== originalSentences) {
    improvements.push("Improved sentence structure");
  }
  
  // Word choice improvements
  const originalWords = original.toLowerCase().split(/\s+/);
  const optimizedWords = optimized.toLowerCase().split(/\s+/);
  
  const wordsChanged = originalWords.filter(word => !optimizedWords.includes(word)).length;
  if (wordsChanged > 0) {
    improvements.push("Enhanced vocabulary and word choice");
  }
  
  // Prompt-specific improvements
  if (prompt.toLowerCase().includes("professional")) {
    improvements.push("Applied professional tone");
  }
  
  if (prompt.toLowerCase().includes("seo")) {
    improvements.push("Optimized for search engines");
  }
  
  if (prompt.toLowerCase().includes("clarity")) {
    improvements.push("Improved clarity and readability");
  }
  
  return improvements.length > 0 ? improvements : ["General content optimization"];
}

function calculateConfidence(original: string, optimized: string, improvements: string[]): number {
  let confidence = 60; // Base confidence for AI optimization
  
  // Boost confidence based on improvements
  confidence += improvements.length * 10;
  
  // Boost if significant changes were made
  const changeRatio = Math.abs(optimized.length - original.length) / original.length;
  if (changeRatio > 0.1) {
    confidence += 15;
  }
  
  // Cap at 95
  return Math.min(confidence, 95);
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": process.env.NODE_ENV === 'production'
        ? (process.env.ALLOWED_ORIGINS || "https://linktrackpro.com")
        : "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400", // 24 hours
    },
  });
}