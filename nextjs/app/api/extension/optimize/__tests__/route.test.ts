import { NextRequest } from 'next/server';

// Test the basic optimization functions directly
describe('Content Optimization API', () => {
  // Test the basic optimization function that we know has issues
  describe('Basic Optimization Function', () => {
    // We'll import the function directly to test it
    // For now, let's create a simple test structure

    it('should handle empty text input gracefully', () => {
      // This test will help us identify the input validation issues
      expect(() => {
        // We'll test this once we can import the function
      }).not.toThrow();
    });

    it('should handle special characters correctly', () => {
      // Test for the regex issues we identified
      expect(true).toBe(true); // Placeholder
    });
  });

  describe('POST /api/extension/optimize', () => {
    const validRequestBody = {
      text: 'This is a test text that needs optimization.',
      prompt: 'Make this text more professional',
      context: 'Business communication',
      max_length: 100,
      tone: 'professional' as const,
    };

    const createRequest = (body: any, headers: Record<string, string> = {}) => {
      return new NextRequest('http://localhost:3000/api/extension/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
        body: JSON.stringify(body),
      });
    };

    describe('Input Validation', () => {
      it('should reject requests with missing required fields', async () => {
        const request = createRequest({
          prompt: 'Make this better',
          // missing 'text' field
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.success).toBe(false);
        expect(data.message).toBe('Validation error');
        expect(data.errors).toBeDefined();
      });

      it('should reject requests with empty text', async () => {
        const request = createRequest({
          text: '',
          prompt: 'Make this better',
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.success).toBe(false);
      });

      it('should reject requests with invalid tone', async () => {
        const request = createRequest({
          text: 'Test text',
          prompt: 'Make this better',
          tone: 'invalid-tone',
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.success).toBe(false);
      });

      it('should handle malformed JSON gracefully', async () => {
        const request = new NextRequest('http://localhost:3000/api/extension/optimize', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: 'invalid json',
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(500);
        expect(data.success).toBe(false);
      });
    });

    describe('Authentication', () => {
      it('should work without authentication (free tier)', async () => {
        const request = createRequest(validRequestBody);
        
        // Mock successful AI API response
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Optimized professional text for business communication.'
              }
            }]
          }),
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.tier).toBe('free');
      });

      it('should handle valid API key authentication', async () => {
        const mockUser = {
          uuid: 'test-user-uuid',
          email: '<EMAIL>',
          nickname: 'Test User',
          avatar_url: 'https://example.com/avatar.jpg',
        };

        (getUserByApiKey as jest.Mock).mockResolvedValueOnce(mockUser);
        
        const request = createRequest(validRequestBody, {
          'Authorization': 'Bearer valid-api-key',
        });

        // Mock successful AI API response
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Optimized professional text for business communication.'
              }
            }]
          }),
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.tier).toBe('premium');
        expect(getUserByApiKey).toHaveBeenCalledWith('valid-api-key');
      });

      it('should handle invalid API key gracefully', async () => {
        (getUserByApiKey as jest.Mock).mockResolvedValueOnce(null);
        
        const request = createRequest(validRequestBody, {
          'Authorization': 'Bearer invalid-api-key',
        });

        // Mock successful AI API response for free tier
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Optimized text.'
              }
            }]
          }),
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.tier).toBe('free'); // Should fallback to free tier
      });
    });

    describe('AI API Integration', () => {
      it('should handle AI API success', async () => {
        const request = createRequest(validRequestBody);
        
        const mockAIResponse = {
          choices: [{
            message: {
              content: 'This is professionally optimized text for business communication purposes.'
            }
          }]
        };

        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockAIResponse),
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.optimized_text).toBe(mockAIResponse.choices[0].message.content);
        expect(data.source).toBe('ai');
        expect(data.improvements).toBeDefined();
        expect(data.confidence).toBeGreaterThan(0);
      });

      it('should fallback to basic optimization when AI API fails', async () => {
        const request = createRequest(validRequestBody);
        
        // Mock AI API failure
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.source).toBe('fallback');
        expect(data.optimized_text).toBeDefined();
        expect(data.improvements).toBeDefined();
      });

      it('should handle malformed AI API response', async () => {
        const request = createRequest(validRequestBody);
        
        // Mock malformed AI response
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            // Missing choices array
            error: 'Invalid request'
          }),
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.source).toBe('fallback');
      });
    });

    describe('Fallback Optimization', () => {
      beforeEach(() => {
        // Remove AI API keys to force fallback
        delete process.env.AI_API_KEY;
        delete process.env.FREE_AI_API_KEY;
      });

      it('should perform basic text optimization', async () => {
        const request = createRequest({
          text: '  this is  a test   text  ',
          prompt: 'Make this professional',
          tone: 'professional',
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.source).toBe('fallback');
        expect(data.optimized_text).toBe('This is a test text.');
        expect(data.improvements).toContain('Normalized whitespace');
        expect(data.improvements).toContain('Capitalized first letter');
        expect(data.improvements).toContain('Added proper punctuation');
      });

      it('should apply professional tone replacements', async () => {
        const request = createRequest({
          text: 'This is awesome stuff guys',
          prompt: 'Make this professional',
          tone: 'professional',
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.optimized_text).toContain('excellent');
        expect(data.optimized_text).toContain('content');
        expect(data.optimized_text).toContain('team');
        expect(data.improvements).toContain('Enhanced professional tone');
      });

      it('should handle length constraints', async () => {
        const longText = 'This is a very long text that exceeds the maximum length limit and should be truncated properly.';
        const request = createRequest({
          text: longText,
          prompt: 'Shorten this',
          max_length: 50,
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.optimized_text.length).toBeLessThanOrEqual(50);
        expect(data.optimized_text).toMatch(/\.\.\.$/);
        expect(data.improvements).toContain('Shortened to 50 characters');
      });
    });

    describe('Error Handling', () => {
      it('should handle network errors gracefully', async () => {
        const request = createRequest(validRequestBody);
        
        // Mock network error
        (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.source).toBe('fallback');
      });

      it('should handle getUserByApiKey errors', async () => {
        (getUserByApiKey as jest.Mock).mockRejectedValueOnce(new Error('Database error'));
        
        const request = createRequest(validRequestBody, {
          'Authorization': 'Bearer some-key',
        });

        // Mock successful AI API response for free tier
        (global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Optimized text.'
              }
            }]
          }),
        });

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.tier).toBe('free'); // Should fallback to free tier
      });
    });
  });

  describe('OPTIONS /api/extension/optimize', () => {
    it('should return correct CORS headers', async () => {
      const response = await OPTIONS();

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('POST, OPTIONS');
      expect(response.headers.get('Access-Control-Allow-Headers')).toBe('Content-Type, Authorization');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty text after validation', async () => {
      const request = createRequest({
        text: '   ',  // Only whitespace
        prompt: 'Optimize this',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });

    it('should handle very long text inputs', async () => {
      const veryLongText = 'A'.repeat(10000);
      const request = createRequest({
        text: veryLongText,
        prompt: 'Optimize this long text',
      });

      // Mock AI API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'Optimized long text.'
            }
          }]
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('should handle special characters in text', async () => {
      const request = createRequest({
        text: 'Text with émojis 🚀 and spëcial chars: @#$%^&*()',
        prompt: 'Clean this up',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('should handle missing authorization header gracefully', async () => {
      const request = createRequest(validRequestBody, {
        'Authorization': '',  // Empty auth header
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.tier).toBe('free');
    });

    it('should handle malformed authorization header', async () => {
      const request = createRequest(validRequestBody, {
        'Authorization': 'InvalidFormat',  // Not "Bearer ..."
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.tier).toBe('free');
    });
  });

  describe('Security Tests', () => {
    it('should not log sensitive API key information', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const request = createRequest({
        text: 'Test text',
        prompt: 'Optimize',
        secretApiKey: 'should-not-be-logged',  // Extra field that shouldn't be logged
      });

      await POST(request);

      // Check that console.log was called but doesn't contain sensitive data
      const logCalls = consoleSpy.mock.calls.flat().join(' ');
      expect(logCalls).not.toContain('should-not-be-logged');

      consoleSpy.mockRestore();
    });

    it('should handle SQL injection attempts in text field', async () => {
      const request = createRequest({
        text: "'; DROP TABLE users; --",
        prompt: 'Clean this malicious input',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      // Should treat it as normal text, not execute any SQL
    });
  });

  describe('Performance Tests', () => {
    it('should handle concurrent requests', async () => {
      const requests = Array.from({ length: 5 }, (_, i) =>
        createRequest({
          text: `Test text ${i}`,
          prompt: 'Optimize this',
        })
      );

      // Mock AI API responses
      (global.fetch as jest.Mock).mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            choices: [{
              message: {
                content: 'Optimized text.'
              }
            }]
          }),
        })
      );

      const responses = await Promise.all(requests.map(req => POST(req)));

      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });
});
